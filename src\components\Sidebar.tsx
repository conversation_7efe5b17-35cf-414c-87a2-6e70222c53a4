'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  LayoutDashboard, 
  FileText, 
  ShoppingCart, 
  DollarSign, 
  Users, 
  BarChart3, 
  Settings, 
  LogOut,
  ChevronDown,
  User
} from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface SidebarProps {
  currentUser: User;
}

export default function Sidebar({ currentUser }: SidebarProps) {
  const [isDocumentsOpen, setIsDocumentsOpen] = useState(true);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    {
      icon: LayoutDashboard,
      label: 'Dashboard',
      href: currentUser.role === 'ADMIN' ? '/admin' : '/dashboard',
      active: true
    },
    {
      icon: FileText,
      label: 'Documents',
      href: '#',
      hasSubmenu: true,
      submenu: [
        {
          label: currentUser.role === 'ADMIN' ? 'All Documents' : 'My Documents',
          href: currentUser.role === 'ADMIN' ? '/admin/documents' : '/dashboard/documents',
          badge: '3'
        },
        {
          label: 'Uploaded Documents',
          href: currentUser.role === 'ADMIN' ? '/admin/documents/uploaded' : '/dashboard/documents/uploaded'
        },
        {
          label: 'Signed Documents', 
          href: currentUser.role === 'ADMIN' ? '/admin/documents/signed' : '/dashboard/documents/signed'
        }
      ]
    }
  ];

  // Add admin-specific menu items
  if (currentUser.role === 'ADMIN') {
    menuItems.push(
      {
        icon: ShoppingCart,
        label: 'Purchase Orders',
        href: '/admin/purchase-orders'
      },
      {
        icon: DollarSign,
        label: 'Currencies',
        href: '/admin/currencies'
      },
      {
        icon: Users,
        label: 'Suppliers',
        href: '/admin/suppliers'
      },
      {
        icon: BarChart3,
        label: 'Reports',
        href: '/admin/reports'
      }
    );
  }

  menuItems.push({
    icon: Settings,
    label: 'Settings',
    href: currentUser.role === 'ADMIN' ? '/admin/settings' : '/dashboard/settings'
  });

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col">
      {/* User Profile Section */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            <User className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {currentUser.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {currentUser.role === 'ADMIN' ? 'Administrator' : 'User'}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {menuItems.map((item, index) => (
          <div key={index}>
            {item.hasSubmenu ? (
              <div>
                <button
                  onClick={() => setIsDocumentsOpen(!isDocumentsOpen)}
                  className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    item.active 
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' 
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </div>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform ${
                      isDocumentsOpen ? 'rotate-180' : ''
                    }`} 
                  />
                </button>
                
                {isDocumentsOpen && item.submenu && (
                  <div className="ml-8 mt-1 space-y-1">
                    {item.submenu.map((subItem, subIndex) => (
                      <a
                        key={subIndex}
                        href={subItem.href}
                        className={`flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors ${
                          subIndex === 0 
                            ? 'bg-red-50 text-red-700 border-l-2 border-red-500' 
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        <span>{subItem.label}</span>
                        {subItem.badge && (
                          <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                            {subItem.badge}
                          </span>
                        )}
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <a
                href={item.href}
                className="flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                <item.icon className="w-5 h-5" />
                <span>{item.label}</span>
              </a>
            )}
          </div>
        ))}
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={handleLogout}
          className="w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <LogOut className="w-5 h-5" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
}
