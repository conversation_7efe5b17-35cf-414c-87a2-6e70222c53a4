import { google } from 'googleapis';

const SCOPES = ['https://www.googleapis.com/auth/drive.file'];

// Initialize Google Drive API
export const getDriveService = () => {
  const auth = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    'http://localhost:3000' // redirect URI
  );

  auth.setCredentials({
    refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
  });

  return google.drive({ version: 'v3', auth });
};

// Upload file to Google Drive
export const uploadToGoogleDrive = async (
  fileBuffer: Buffer,
  fileName: string,
  mimeType: string,
  folderId?: string
) => {
  try {
    const drive = getDriveService();

    const fileMetadata: any = {
      name: fileName,
    };

    if (folderId) {
      fileMetadata.parents = [folderId];
    }

    const media = {
      mimeType,
      body: Buffer.from(fileBuffer),
    };

    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id,name,webViewLink,webContentLink',
    });

    return response.data;
  } catch (error) {
    console.error('Error uploading to Google Drive:', error);
    throw error;
  }
};

// Download file from Google Drive
export const downloadFromGoogleDrive = async (fileId: string) => {
  try {
    const drive = getDriveService();
    
    const response = await drive.files.get({
      fileId,
      alt: 'media',
    });

    return response.data;
  } catch (error) {
    console.error('Error downloading from Google Drive:', error);
    throw error;
  }
};

// Get file metadata from Google Drive
export const getFileMetadata = async (fileId: string) => {
  try {
    const drive = getDriveService();
    
    const response = await drive.files.get({
      fileId,
      fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink',
    });

    return response.data;
  } catch (error) {
    console.error('Error getting file metadata:', error);
    throw error;
  }
};

// Delete file from Google Drive
export const deleteFromGoogleDrive = async (fileId: string) => {
  try {
    const drive = getDriveService();
    
    await drive.files.delete({
      fileId,
    });

    return true;
  } catch (error) {
    console.error('Error deleting from Google Drive:', error);
    throw error;
  }
};

// Create folder in Google Drive
export const createGoogleDriveFolder = async (folderName: string, parentFolderId?: string) => {
  try {
    const drive = getDriveService();

    const fileMetadata: any = {
      name: folderName,
      mimeType: 'application/vnd.google-apps.folder',
    };

    if (parentFolderId) {
      fileMetadata.parents = [parentFolderId];
    }

    const response = await drive.files.create({
      requestBody: fileMetadata,
      fields: 'id,name',
    });

    return response.data;
  } catch (error) {
    console.error('Error creating Google Drive folder:', error);
    throw error;
  }
};

// List files in a folder
export const listFilesInFolder = async (folderId: string) => {
  try {
    const drive = getDriveService();
    
    const response = await drive.files.list({
      q: `'${folderId}' in parents and trashed=false`,
      fields: 'files(id,name,mimeType,size,createdTime,modifiedTime,webViewLink)',
    });

    return response.data.files || [];
  } catch (error) {
    console.error('Error listing files in folder:', error);
    throw error;
  }
};
