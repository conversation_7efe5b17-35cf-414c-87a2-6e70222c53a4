import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { downloadFromGoogleDrive } from '@/lib/google-drive';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const documentId = params.id;

    // Get document from database
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      include: {
        uploadedBy: true,
      },
    });

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check permissions - users can only download their own documents, admins can download any
    if (payload.role !== 'ADMIN' && document.uploadedById !== payload.userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Determine which file to download (signed version if available, otherwise original)
    const fileId = document.signedGoogleDriveId || document.googleDriveId;
    
    if (!fileId) {
      return NextResponse.json({ error: 'File not available' }, { status: 404 });
    }

    try {
      // Download file from Google Drive
      const fileData = await downloadFromGoogleDrive(fileId);
      
      // Determine filename
      const filename = document.signedGoogleDriveId 
        ? `signed_${document.originalFileName}`
        : document.originalFileName;

      // Return file as response
      return new NextResponse(fileData as any, {
        headers: {
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Type': 'application/octet-stream',
        },
      });
    } catch (driveError) {
      console.error('Google Drive download error:', driveError);
      return NextResponse.json(
        { error: 'Failed to download file from Google Drive' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Download error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
