import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { uploadToGoogleDrive } from '@/lib/google-drive';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication and admin role
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || payload.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const documentId = params.id;

    // Check if document exists
    const document = await prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    if (document.status === 'SIGNED') {
      return NextResponse.json({ error: 'Document already signed' }, { status: 400 });
    }

    // Parse form data
    const formData = await request.formData();
    const signedFile = formData.get('signedFile') as File;
    const feedback = formData.get('feedback') as string;

    if (!signedFile) {
      return NextResponse.json({ error: 'Signed file is required' }, { status: 400 });
    }

    // Validate file size (10MB limit)
    if (signedFile.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await signedFile.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename for signed document
    const timestamp = Date.now();
    const signedFileName = `signed_${timestamp}_${signedFile.name}`;

    // Upload signed document to Google Drive
    let signedGoogleDriveId = null;
    try {
      const signedDriveFile = await uploadToGoogleDrive(
        buffer,
        signedFileName,
        signedFile.type,
        process.env.GOOGLE_DRIVE_SIGNED_FOLDER_ID
      );
      
      signedGoogleDriveId = signedDriveFile.id;
    } catch (driveError) {
      console.error('Google Drive upload error:', driveError);
      return NextResponse.json(
        { error: 'Failed to upload signed document to Google Drive' },
        { status: 500 }
      );
    }

    // Update document in database
    const updatedDocument = await prisma.document.update({
      where: { id: documentId },
      data: {
        status: 'SIGNED',
        signedGoogleDriveId,
        feedback: feedback || null,
        signedAt: new Date(),
        folder: 'SIGNED', // Move to signed folder
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Document signed successfully',
      document: updatedDocument,
    });
  } catch (error) {
    console.error('Document signing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
