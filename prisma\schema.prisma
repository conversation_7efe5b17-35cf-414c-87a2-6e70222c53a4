// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  uploadedDocuments Document[] @relation("UserDocuments")

  @@map("users")
}

model Document {
  id              String           @id @default(cuid())
  title           String
  subject         String
  description     String?
  urgency         UrgencyLevel     @default(NONE)
  status          DocumentStatus   @default(PENDING)
  originalFileName String
  googleDriveId   String?
  signedGoogleDriveId String?
  folder          DocumentFolder   @default(UPLOADED)
  feedback        String?

  // Timestamps
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  signedAt        DateTime?

  // Relations
  uploadedBy      User             @relation("UserDocuments", fields: [uploadedById], references: [id])
  uploadedById    String

  @@map("documents")
}

enum Role {
  USER
  ADMIN
}

enum UrgencyLevel {
  URGENT
  NEUTRAL
  NONE
}

enum DocumentStatus {
  PENDING
  SIGNED
  REJECTED
  NEEDS_REVISION
}

enum DocumentFolder {
  UPLOADED
  SIGNED
}
