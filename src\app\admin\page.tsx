'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Download, 
  Eye, 
  Upload,
  MessageSquare,
  X
} from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { formatDate, getUrgencyColor, getStatusColor } from '@/lib/utils';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface Document {
  id: string;
  title: string;
  subject: string;
  description?: string;
  urgency: string;
  status: string;
  originalFileName: string;
  folder: string;
  feedback?: string;
  googleDriveId?: string;
  signedGoogleDriveId?: string;
  createdAt: string;
  signedAt?: string;
  uploadedBy: {
    id: string;
    name: string;
    email: string;
  };
}

export default function AdminDashboard() {
  const [user, setUser] = useState<User | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isSigningModalOpen, setIsSigningModalOpen] = useState(false);
  const [signedFile, setSignedFile] = useState<File | null>(null);
  const [feedback, setFeedback] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();

  useEffect(() => {
    fetchUser();
    fetchDocuments();
  }, []);

  const fetchUser = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const data = await response.json();
        if (data.user.role !== 'ADMIN') {
          router.push('/dashboard');
          return;
        }
        setUser(data.user);
      } else {
        router.push('/login');
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      router.push('/login');
    }
  };

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents');
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocument = async (document: Document) => {
    if (document.googleDriveId) {
      // Open Google Drive file in new tab
      window.open(`https://drive.google.com/file/d/${document.googleDriveId}/view`, '_blank');
    }
  };

  const handleDownloadDocument = async (document: Document) => {
    if (document.googleDriveId) {
      try {
        const response = await fetch(`/api/documents/${document.id}/download`);
        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = document.originalFileName;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        }
      } catch (error) {
        console.error('Download error:', error);
      }
    }
  };

  const openSigningModal = (document: Document) => {
    setSelectedDocument(document);
    setIsSigningModalOpen(true);
    setSignedFile(null);
    setFeedback('');
  };

  const closeSigningModal = () => {
    setIsSigningModalOpen(false);
    setSelectedDocument(null);
    setSignedFile(null);
    setFeedback('');
  };

  const handleSignDocument = async () => {
    if (!selectedDocument || !signedFile) return;

    setIsProcessing(true);
    
    try {
      const formData = new FormData();
      formData.append('signedFile', signedFile);
      formData.append('feedback', feedback);

      const response = await fetch(`/api/documents/${selectedDocument.id}/sign`, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        await fetchDocuments(); // Refresh documents
        closeSigningModal();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to sign document');
      }
    } catch (error) {
      console.error('Sign document error:', error);
      alert('Failed to sign document');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProvideFeedback = async (document: Document, feedbackText: string) => {
    try {
      const response = await fetch(`/api/documents/${document.id}/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ feedback: feedbackText }),
      });

      if (response.ok) {
        await fetchDocuments(); // Refresh documents
      }
    } catch (error) {
      console.error('Feedback error:', error);
    }
  };

  const getStatusStats = () => {
    const stats = {
      total: documents.length,
      pending: documents.filter(d => d.status === 'PENDING').length,
      signed: documents.filter(d => d.status === 'SIGNED').length,
      urgent: documents.filter(d => d.urgency === 'URGENT').length,
    };
    return stats;
  };

  if (!user) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  const stats = getStatusStats();

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar currentUser={user} />
      
      <div className="flex-1 overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600">Manage and sign documents</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Signature</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.pending}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Signed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.signed}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Urgent</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats.urgent}</div>
              </CardContent>
            </Card>
          </div>

          {/* Documents Table */}
          <Card>
            <CardHeader>
              <CardTitle>All Documents</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">Loading documents...</div>
              ) : documents.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No documents found.
                </div>
              ) : (
                <div className="space-y-4">
                  {documents.map((document) => (
                    <div
                      key={document.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-5 h-5 text-gray-400" />
                          <div>
                            <h3 className="font-medium text-gray-900">{document.subject}</h3>
                            <p className="text-sm text-gray-500">
                              {document.originalFileName} • Uploaded by {document.uploadedBy.name}
                            </p>
                            {document.description && (
                              <p className="text-sm text-gray-600 mt-1">{document.description}</p>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(document.urgency)}`}>
                          {document.urgency.toLowerCase()}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                          {document.status.toLowerCase()}
                        </span>
                        <span className="text-sm text-gray-500">
                          {formatDate(document.createdAt)}
                        </span>
                        
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDocument(document)}
                            disabled={!document.googleDriveId}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownloadDocument(document)}
                            disabled={!document.googleDriveId}
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          {document.status === 'PENDING' && (
                            <Button
                              size="sm"
                              onClick={() => openSigningModal(document)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Sign
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Signing Modal */}
      {isSigningModalOpen && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Sign Document</h2>
              <button
                onClick={closeSigningModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">{selectedDocument.subject}</h3>
                <p className="text-sm text-gray-500">{selectedDocument.originalFileName}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload Signed Document *
                </label>
                <input
                  type="file"
                  onChange={(e) => setSignedFile(e.target.files?.[0] || null)}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Feedback (Optional)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Provide feedback or notes..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={closeSigningModal}
                  className="flex-1"
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSignDocument}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                  disabled={!signedFile || isProcessing}
                >
                  {isProcessing ? 'Processing...' : 'Sign Document'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
