import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { uploadToGoogleDrive } from '@/lib/google-drive';
import { UrgencyLevel, DocumentFolder } from '@prisma/client';

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const subject = formData.get('subject') as string;
    const description = formData.get('description') as string;
    const urgency = formData.get('urgency') as UrgencyLevel;
    const folder = formData.get('folder') as DocumentFolder;

    if (!file || !subject) {
      return NextResponse.json(
        { error: 'File and subject are required' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/jpg',
      'image/png'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF, DOC, DOCX, TXT, JPG, PNG files are allowed' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name}`;

    // Upload to Google Drive
    let googleDriveId = null;
    try {
      const folderId = folder === 'UPLOADED' 
        ? process.env.GOOGLE_DRIVE_UPLOADED_FOLDER_ID 
        : process.env.GOOGLE_DRIVE_SIGNED_FOLDER_ID;

      const driveFile = await uploadToGoogleDrive(
        buffer,
        fileName,
        file.type,
        folderId
      );
      
      googleDriveId = driveFile.id;
    } catch (driveError) {
      console.error('Google Drive upload error:', driveError);
      // Continue without Google Drive if it fails
    }

    // Save document to database
    const document = await prisma.document.create({
      data: {
        title: fileName,
        subject,
        description: description || null,
        urgency: urgency || 'NONE',
        originalFileName: file.name,
        googleDriveId,
        folder: folder || 'UPLOADED',
        uploadedById: payload.userId,
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Document uploaded successfully',
      document,
    });
  } catch (error) {
    console.error('Document upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
