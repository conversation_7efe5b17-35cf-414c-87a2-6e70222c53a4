# Document Signing System

A comprehensive document signing system built with Next.js, featuring dual-role functionality (User/Admin), Google Drive integration, and a modern interface matching your specifications.

## Features

### 🔐 Authentication System
- **Dual Role System**: User and Admin roles with different permissions
- **Secure Authentication**: JWT-based authentication with HTTP-only cookies
- **Role-based Access Control**: Automatic redirection based on user role

### 📄 Document Management
- **File Upload**: Drag-and-drop file upload with support for PDF, DOC, DOCX, TXT, JPG, PNG
- **Popup Form**: Upload form with subject, description, and urgency indicators (Urgent/Neutral/None)
- **Folder Organization**: Separate folders for uploaded and signed documents
- **Google Drive Integration**: Automatic sync with Google Drive using folder IDs

### 👤 User Features
- **Dashboard**: Overview of document statistics and recent uploads
- **Document Upload**: Easy file upload with metadata
- **Document Tracking**: View status of uploaded documents
- **Download Access**: Download original and signed documents

### 👨‍💼 Admin Features
- **Admin Dashboard**: Comprehensive view of all documents
- **Document Review**: View and download documents from Google Drive
- **Document Signing**: Upload signed versions of documents
- **Feedback System**: Provide feedback for documents needing revision
- **Status Management**: Mark documents as signed, rejected, or needing revision

### 🎨 Modern Interface
- **Sidebar Navigation**: Clean sidebar matching your provided design
- **Responsive Design**: Works on desktop and mobile devices
- **Status Indicators**: Color-coded urgency and status badges
- **Real-time Updates**: Automatic refresh of document lists

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (easily configurable to PostgreSQL/MySQL)
- **Authentication**: JWT with bcryptjs
- **File Storage**: Google Drive API
- **UI Components**: Custom components with Radix UI primitives
- **Icons**: Lucide React

## Prerequisites

Before setting up the project, ensure you have:

1. **Node.js** (v18 or higher)
2. **npm** or **yarn**
3. **Google Cloud Console** account for Google Drive API
4. **Git** for version control

## Google Drive API Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Drive API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Drive API"
   - Click "Enable"

### 2. Create Service Account

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the service account details
4. Download the JSON key file
5. Extract the following values:
   - `client_id`
   - `client_secret`
   - `refresh_token` (you'll need to generate this)

### 3. Generate Refresh Token

You can use the [Google OAuth 2.0 Playground](https://developers.google.com/oauthplayground/) to generate a refresh token:

1. Go to OAuth 2.0 Playground
2. Click the gear icon and check "Use your own OAuth credentials"
3. Enter your Client ID and Client Secret
4. In Step 1, select "Google Drive API v3" > "https://www.googleapis.com/auth/drive.file"
5. Click "Authorize APIs"
6. In Step 2, click "Exchange authorization code for tokens"
7. Copy the refresh token

### 4. Create Google Drive Folders

1. Create two folders in your Google Drive:
   - "Uploaded Documents"
   - "Signed Documents"
2. Get the folder IDs from the URL when viewing each folder
3. Share these folders with your service account email

## Installation & Setup

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd sign
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Create a `.env.local` file in the root directory:

```env
# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-in-production"

# Google Drive API
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REFRESH_TOKEN="your-google-refresh-token"

# JWT Secret
JWT_SECRET="your-jwt-secret-here-change-in-production"

# Google Drive Folder IDs
GOOGLE_DRIVE_UPLOADED_FOLDER_ID="your-uploaded-folder-id"
GOOGLE_DRIVE_SIGNED_FOLDER_ID="your-signed-folder-id"
```

### 4. Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev --name init

# Seed the database with test users
npx tsx scripts/seed.ts
```

### 5. Start Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## Default Test Accounts

After running the seed script, you can use these accounts:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

**User Account:**
- Email: `<EMAIL>`
- Password: `user123`

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard
│   ├── dashboard/         # User dashboard
│   ├── login/             # Login page
│   ├── register/          # Registration page
│   └── api/               # API routes
│       ├── auth/          # Authentication endpoints
│       └── documents/     # Document management endpoints
├── components/            # React components
│   ├── ui/               # Base UI components
│   ├── Sidebar.tsx       # Navigation sidebar
│   └── DocumentUploadModal.tsx
├── lib/                  # Utility libraries
│   ├── auth.ts          # Authentication utilities
│   ├── db.ts            # Database connection
│   ├── google-drive.ts  # Google Drive integration
│   └── utils.ts         # General utilities
└── middleware.ts        # Next.js middleware for auth
```

## Deployment

### GitHub Setup

1. **Initialize Git Repository**
```bash
git init
git add .
git commit -m "Initial commit: Document signing system"
```

2. **Create GitHub Repository**
- Go to [GitHub](https://github.com) and create a new repository
- Follow the instructions to push your local repository

```bash
git remote add origin https://github.com/yourusername/document-signing-system.git
git branch -M main
git push -u origin main
```

### Vercel Deployment

1. **Connect to Vercel**
- Go to [Vercel](https://vercel.com)
- Sign in with your GitHub account
- Click "New Project" and import your repository

2. **Environment Variables**
Add the following environment variables in Vercel dashboard:

```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-app-name.vercel.app"
NEXTAUTH_SECRET="your-production-secret"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REFRESH_TOKEN="your-google-refresh-token"
JWT_SECRET="your-production-jwt-secret"
GOOGLE_DRIVE_UPLOADED_FOLDER_ID="your-uploaded-folder-id"
GOOGLE_DRIVE_SIGNED_FOLDER_ID="your-signed-folder-id"
```

3. **Database Setup for Production**
For production, consider using:
- **PostgreSQL**: Vercel Postgres, Supabase, or Railway
- **MySQL**: PlanetScale or Railway
- **SQLite**: Turso for serverless SQLite

Update your `prisma/schema.prisma` datasource for production:

```prisma
datasource db {
  provider = "postgresql" // or mysql
  url      = env("DATABASE_URL")
}
```

4. **Deploy**
- Vercel will automatically deploy when you push to main branch
- Run database migrations in production:

```bash
npx prisma migrate deploy
npx prisma generate
```

### Production Considerations

1. **Security**
- Use strong, unique secrets for JWT and NextAuth
- Enable HTTPS in production
- Implement rate limiting for API endpoints

2. **Database**
- Use a production-ready database
- Set up regular backups
- Monitor database performance

3. **Google Drive**
- Use a dedicated Google Cloud project for production
- Set up proper IAM permissions
- Monitor API usage and quotas

4. **Monitoring**
- Set up error tracking (Sentry, LogRocket)
- Monitor application performance
- Set up uptime monitoring

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Documents
- `GET /api/documents` - Get documents (filtered by role)
- `POST /api/documents/upload` - Upload new document
- `GET /api/documents/[id]/download` - Download document
- `POST /api/documents/[id]/sign` - Sign document (admin only)
- `POST /api/documents/[id]/feedback` - Provide feedback (admin only)

## Usage Guide

### For Users
1. **Register/Login**: Create account or login with existing credentials
2. **Upload Documents**: Click "Upload Document" and fill in the form
3. **Track Status**: Monitor document status in dashboard
4. **Download**: Download original or signed documents

### For Admins
1. **Review Documents**: View all uploaded documents in admin dashboard
2. **Download & Review**: Download documents for review
3. **Sign Documents**: Upload signed versions of documents
4. **Provide Feedback**: Give feedback for documents needing revision

## Troubleshooting

### Common Issues

1. **Google Drive API Errors**
- Check if API is enabled in Google Cloud Console
- Verify service account permissions
- Ensure folder IDs are correct

2. **Database Connection Issues**
- Verify DATABASE_URL is correct
- Run `npx prisma generate` after schema changes
- Check if migrations are applied

3. **Authentication Issues**
- Verify JWT_SECRET is set
- Check if cookies are being set correctly
- Ensure NEXTAUTH_URL matches your domain

### Development Tips

1. **Database Reset**
```bash
npx prisma migrate reset
npx tsx scripts/seed.ts
```

2. **View Database**
```bash
npx prisma studio
```

3. **Check Logs**
- Check browser console for frontend errors
- Check terminal for backend errors
- Use Vercel logs for production issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the GitHub repository
- Check the troubleshooting section
- Review the API documentation
